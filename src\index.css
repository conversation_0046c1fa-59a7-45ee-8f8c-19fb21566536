@tailwind base;
@tailwind components;
@tailwind utilities;

/* CSS Variables for Theme System */
:root {
  /* Dark Theme Variables (Default) */
  --bg-primary: #111827;
  --bg-secondary: #1f2937;
  --bg-tertiary: #374151;
  --text-primary: #ffffff;
  --text-secondary: #d1d5db;
  --text-tertiary: #9ca3af;
  
  /* NEW DARK GREEN MILITARY COLORS */
  --accent-primary: #2C5F2D;      /* Primary green accent (replaces main gold) */
  --accent-secondary: #1B4B1C;    /* Secondary green accent (replaces secondary gold) */
  --accent-light: #A8C4A2;        /* Light green accent (replaces light gold) */
  --accent-hover: #4A5D23;        /* Hover state green (replaces gold hover) */
  --accent-subtle: #98B89A;       /* Subtle green backgrounds */
  --accent-pine: #1C3B1D;         /* Dark pine green */
  --accent-olive: #556B2F;        /* Olive green tertiary */
  
  --border-primary: #4b5563;
  --border-secondary: #6b7280;
  --shadow-color: rgba(0, 0, 0, 0.3);
}

/* Light Theme Variables */
.light-mode {
  --bg-primary: #ffffff;
  --bg-secondary: #f9fafb;
  --bg-tertiary: #f3f4f6;
  --text-primary: #1f2937;
  --text-secondary: #374151;
  --text-tertiary: #6b7280;
  
  /* LIGHT MODE GREEN COLORS */
  --accent-primary: #2C5F2D;      /* Primary green accent */
  --accent-secondary: #1B4B1C;    /* Secondary green accent */
  --accent-light: #A8C4A2;        /* Light green accent */
  --accent-hover: #4A5D23;        /* Hover state green */
  --accent-subtle: #98B89A;       /* Subtle green backgrounds */
  --accent-pine: #1C3B1D;         /* Dark pine green */
  --accent-olive: #556B2F;        /* Olive green tertiary */
  
  --border-primary: #e5e7eb;
  --border-secondary: #d1d5db;
  --shadow-color: rgba(0, 0, 0, 0.1);
}

/* Global Transitions */
* {
  transition: background-color 0.3s ease, color 0.3s ease, border-color 0.3s ease, box-shadow 0.3s ease;
}

/* Base Styles */
body {
  background-color: var(--bg-primary);
  color: var(--text-primary);
}

/* Theme-aware Utility Classes */
.bg-theme-primary {
  background-color: var(--bg-primary);
}

.bg-theme-secondary {
  background-color: var(--bg-secondary);
}

.bg-theme-tertiary {
  background-color: var(--bg-tertiary);
}

.text-theme-primary {
  color: var(--text-primary);
}

.text-theme-secondary {
  color: var(--text-secondary);
}

.text-theme-tertiary {
  color: var(--text-tertiary);
}

.border-theme-primary {
  border-color: var(--border-primary);
}

.border-theme-secondary {
  border-color: var(--border-secondary);
}

/* Light Mode Overrides for Existing Classes */
.light-mode .bg-gray-900 {
  background-color: var(--bg-primary) !important;
}

.light-mode .bg-gray-800 {
  background-color: var(--bg-secondary) !important;
}

.light-mode .bg-gray-700 {
  background-color: var(--bg-tertiary) !important;
}

.light-mode .text-white {
  color: var(--text-primary) !important;
}

.light-mode .text-gray-300 {
  color: var(--text-secondary) !important;
}

.light-mode .text-gray-400 {
  color: var(--text-tertiary) !important;
}

.light-mode .border-gray-800 {
  border-color: var(--border-primary) !important;
}

.light-mode .border-gray-700 {
  border-color: var(--border-secondary) !important;
}

.light-mode .border-gray-600 {
  border-color: var(--border-secondary) !important;
}

/* Light Mode Gradient Overrides */
.light-mode .from-gray-900 {
  --tw-gradient-from: var(--bg-primary) !important;
}

.light-mode .to-gray-800 {
  --tw-gradient-to: var(--bg-secondary) !important;
}

/* Light Mode Shadow Overrides */
.light-mode .shadow-xl {
  box-shadow: 0 20px 25px -5px var(--shadow-color), 0 10px 10px -5px var(--shadow-color) !important;
}

.light-mode .shadow-2xl {
  box-shadow: 0 25px 50px -12px var(--shadow-color) !important;
}

.light-mode .shadow-lg {
  box-shadow: 0 10px 15px -3px var(--shadow-color), 0 4px 6px -2px var(--shadow-color) !important;
}

/* Light Mode Form Elements */
.light-mode input,
.light-mode textarea,
.light-mode select {
  background-color: var(--bg-primary) !important;
  color: var(--text-primary) !important;
  border-color: var(--border-primary) !important;
}

.light-mode input:focus,
.light-mode textarea:focus,
.light-mode select:focus {
  border-color: var(--accent-primary) !important;
  box-shadow: 0 0 0 3px rgba(44, 95, 45, 0.1) !important;
}

.light-mode input::placeholder,
.light-mode textarea::placeholder {
  color: var(--text-tertiary) !important;
}

/* GREEN THEME BUTTON OVERRIDES - Replace All Gold/Yellow with Green */
.bg-gradient-to-r.from-yellow-400.to-yellow-600,
.light-mode .bg-gradient-to-r.from-yellow-400.to-yellow-600 {
  background: linear-gradient(to right, var(--accent-primary), var(--accent-secondary)) !important;
  color: white !important;
}

.hover\:from-yellow-500:hover,
.hover\:to-yellow-700:hover,
.light-mode .hover\:from-yellow-500:hover,
.light-mode .hover\:to-yellow-700:hover {
  background: linear-gradient(to right, var(--accent-hover), var(--accent-pine)) !important;
}

.border-yellow-400,
.light-mode .border-yellow-400 {
  border-color: var(--accent-primary) !important;
}

.text-yellow-400,
.light-mode .text-yellow-400 {
  color: var(--accent-primary) !important;
}

.hover\:bg-yellow-400:hover,
.light-mode .hover\:bg-yellow-400:hover {
  background-color: var(--accent-primary) !important;
  color: white !important;
}

.hover\:text-yellow-400:hover,
.light-mode .hover\:text-yellow-400:hover {
  color: var(--accent-primary) !important;
}

.hover\:text-yellow-300:hover,
.light-mode .hover\:text-yellow-300:hover {
  color: var(--accent-light) !important;
}

/* GREEN THEME NAVIGATION OVERRIDES */
.light-mode nav {
  background-color: rgba(255, 255, 255, 0.95) !important;
  backdrop-filter: blur(10px);
  border-bottom: 1px solid var(--border-primary) !important;
}

/* GREEN THEME CARDS AND CONTAINERS */
.light-mode .bg-gray-900\/95 {
  background-color: rgba(255, 255, 255, 0.95) !important;
}

.light-mode .bg-gray-800\/50 {
  background-color: rgba(249, 250, 251, 0.5) !important;
}

/* GREEN THEME HERO SECTION */
.light-mode .hero-overlay {
  background: linear-gradient(to right, rgba(255, 255, 255, 0.8), rgba(249, 250, 251, 0.6)) !important;
}

/* GREEN THEME HOVER STATES */
.light-mode .hover\:bg-gray-700:hover {
  background-color: var(--bg-tertiary) !important;
}

.light-mode .hover\:bg-gray-800:hover {
  background-color: var(--bg-secondary) !important;
}

/* GREEN THEME BACKDROP BLUR */
.light-mode .backdrop-blur-sm {
  backdrop-filter: blur(4px);
}

/* GREEN THEME SCROLLBAR */
.light-mode ::-webkit-scrollbar {
  width: 8px;
}

.light-mode ::-webkit-scrollbar-track {
  background: var(--bg-secondary);
}

.light-mode ::-webkit-scrollbar-thumb {
  background: var(--border-secondary);
  border-radius: 4px;
}

.light-mode ::-webkit-scrollbar-thumb:hover {
  background: var(--text-tertiary);
}

/* Ensure proper contrast for images in light mode */
.light-mode img {
  filter: brightness(1.05) contrast(1.02);
}

/* Image Performance Optimizations */
img {
  /* Enable hardware acceleration for smoother rendering */
  transform: translateZ(0);
  backface-visibility: hidden;

  /* Optimize image rendering */
  image-rendering: -webkit-optimize-contrast;
  image-rendering: crisp-edges;

  /* Prevent layout shifts */
  height: auto;
  max-width: 100%;

  /* Smooth loading transitions */
  transition: opacity 0.3s ease-in-out, transform 0.3s ease-in-out;
}

/* Optimized image containers */
.image-container {
  /* Contain layout shifts */
  contain: layout style paint;

  /* Enable GPU acceleration */
  will-change: transform;
  transform: translateZ(0);

  /* Optimize repaints */
  isolation: isolate;
}

/* Fast loading states */
.image-loading {
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: loading 1.5s infinite;
}

@keyframes loading {
  0% {
    background-position: 200% 0;
  }
  100% {
    background-position: -200% 0;
  }
}

/* Dark mode loading states */
.dark .image-loading {
  background: linear-gradient(90deg, #374151 25%, #4b5563 50%, #374151 75%);
  background-size: 200% 100%;
}

/* Optimized rounded images */
.rounded-image {
  border-radius: 50%;
  /* Optimize circular clipping */
  clip-path: circle(50%);
  transform: translateZ(0);
}

/* Gallery image optimizations */
.gallery-image {
  /* Optimize for gallery grid */
  contain: layout style paint;
  will-change: transform, opacity;

  /* Smooth hover effects */
  transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.gallery-image:hover {
  transform: translateY(-4px) translateZ(0);
}

/* GREEN THEME GRADIENT TEXT */
.text-transparent.bg-clip-text.bg-gradient-to-r.from-yellow-400.to-yellow-600,
.light-mode .text-transparent.bg-clip-text.bg-gradient-to-r.from-yellow-400.to-yellow-600 {
  background: linear-gradient(to right, var(--accent-primary), var(--accent-secondary));
  -webkit-background-clip: text;
  background-clip: text;
  color: transparent;
}

/* Military Gradient Text - Direct gradient application without background bar */
.gradient-text-military {
  background: linear-gradient(to right, var(--accent-primary), var(--accent-secondary));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  color: transparent;
  display: inline-block;
}

/* Hero Tagline with subtle black text outline */
.hero-tagline {
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.3);
}

/* Hero text should always be white regardless of theme */
.hero-text-white,
.light-mode .hero-text-white {
  color: white !important;
}

/* GREEN THEME SPECIFIC COMPONENT STYLES */

/* Primary Buttons */
.btn-primary,
.bg-yellow-400 {
  background-color: var(--accent-primary) !important;
  border-color: var(--accent-primary) !important;
  color: white !important;
}

.btn-primary:hover,
.bg-yellow-400:hover {
  background-color: var(--accent-hover) !important;
  border-color: var(--accent-hover) !important;
}

/* Secondary Buttons */
.btn-secondary {
  border-color: var(--accent-primary) !important;
  color: var(--accent-primary) !important;
}

.btn-secondary:hover {
  background-color: var(--accent-light) !important;
  color: white !important;
}

/* Navigation Links */
.nav-link:hover {
  color: var(--accent-primary) !important;
}

.nav-link.active {
  color: var(--accent-primary) !important;
}

/* Form Controls */
.form-control:focus {
  border-color: var(--accent-primary) !important;
  box-shadow: 0 0 0 0.2rem rgba(44, 95, 45, 0.25) !important;
}

/* Card Accents */
.card-accent {
  border-left: 4px solid var(--accent-primary) !important;
}

.card-accent-border {
  border-color: var(--accent-primary) !important;
}

/* Text Accents */
.text-accent {
  color: var(--accent-primary) !important;
}

.text-accent-light {
  color: var(--accent-light) !important;
}

/* Icon Colors */
.icon-accent {
  color: var(--accent-primary) !important;
}

.icon-accent-light {
  color: var(--accent-light) !important;
}

/* Background Accents */
.bg-accent {
  background-color: var(--accent-primary) !important;
}

.bg-accent-light {
  background-color: var(--accent-light) !important;
}

.bg-accent-subtle {
  background-color: var(--accent-subtle) !important;
}

/* Border Accents */
.border-accent {
  border-color: var(--accent-primary) !important;
}

.border-accent-light {
  border-color: var(--accent-light) !important;
}

/* Hover States for Green Theme */
.hover-accent:hover {
  background-color: var(--accent-primary) !important;
  color: white !important;
}

.hover-accent-border:hover {
  border-color: var(--accent-primary) !important;
}

.hover-accent-text:hover {
  color: var(--accent-primary) !important;
}

/* Focus States for Green Theme */
.focus-accent:focus {
  border-color: var(--accent-primary) !important;
  box-shadow: 0 0 0 3px rgba(44, 95, 45, 0.1) !important;
}

/* Active States for Green Theme */
.active-accent {
  background-color: var(--accent-secondary) !important;
  color: white !important;
}

/* Green Theme Status Indicators */
.status-ongoing {
  background-color: var(--accent-primary) !important;
  color: white !important;
}

.status-completed {
  background-color: var(--accent-secondary) !important;
  color: white !important;
}

/* Green Theme Shadows */
.shadow-accent {
  box-shadow: 0 4px 20px rgba(44, 95, 45, 0.15) !important;
}

.shadow-accent-lg {
  box-shadow: 0 10px 30px rgba(44, 95, 45, 0.2) !important;
}

/* Green Theme Gradients */
.gradient-accent {
  background: linear-gradient(135deg, var(--accent-primary), var(--accent-secondary)) !important;
}

.gradient-accent-light {
  background: linear-gradient(135deg, var(--accent-light), var(--accent-subtle)) !important;
}

/* Green Theme for Theme Toggle */
.theme-toggle-btn {
  background-color: var(--bg-tertiary) !important;
  border-color: var(--border-primary) !important;
}

.theme-toggle-btn:hover {
  background-color: var(--accent-primary) !important;
  border-color: var(--accent-primary) !important;
}

/* Green Theme for Special Elements */
.military-accent {
  color: var(--accent-primary) !important;
}

.military-bg {
  background-color: var(--accent-primary) !important;
}

.military-border {
  border-color: var(--accent-primary) !important;
}

/* Green Theme for CTA Sections */
.cta-primary {
  background: linear-gradient(to right, var(--accent-primary), var(--accent-secondary)) !important;
}

.cta-secondary {
  background: linear-gradient(to right, var(--accent-light), var(--accent-subtle)) !important;
}

/* Ensure all yellow/gold references are converted to green */
.bg-yellow-500,
.bg-yellow-600,
.text-yellow-500,
.text-yellow-600,
.border-yellow-500,
.border-yellow-600 {
  background-color: var(--accent-primary) !important;
  color: var(--accent-primary) !important;
  border-color: var(--accent-primary) !important;
}

/* Light mode specific green overrides */
.light-mode .bg-yellow-500,
.light-mode .bg-yellow-600,
.light-mode .text-yellow-500,
.light-mode .text-yellow-600,
.light-mode .border-yellow-500,
.light-mode .border-yellow-600 {
  background-color: var(--accent-primary) !important;
  color: var(--accent-primary) !important;
  border-color: var(--accent-primary) !important;
}